Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Collecting dashscope
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/1a/5e/58eb42ba9d23dbb2bb37032b4991fcdf5b0e42fcb624c3f095b253f490d3/dashscope-1.24.1-py3-none-any.whl (1.3 MB)
     ---------------------------------------- 1.3/1.3 MB 9.2 MB/s eta 0:00:00
Collecting aiohttp (from dashscope)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/2b/d8/fa65d2a349fe938b76d309db1a56a75c4fb8cc7b17a398b698488a939903/aiohttp-3.12.15-cp312-cp312-win_amd64.whl (450 kB)
Requirement already satisfied: requests in d:\pyenv\pyenv-win\versions\3.12.3\lib\site-packages (from dashscope) (2.32.4)
Collecting websocket-client (from dashscope)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/5a/84/44687a29792a70e111c5c477230a72c4b957d88d16141199bf9acb7537a3/websocket_client-1.8.0-py3-none-any.whl (58 kB)
     ---------------------------------------- 58.8/58.8 kB 3.0 MB/s eta 0:00:00
Collecting cryptography (from dashscope)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/1f/10/197da38a5911a48dd5389c043de4aec4b3c94cb836299b01253940788d78/cryptography-45.0.5-cp311-abi3-win_amd64.whl (3.4 MB)
     ---------------------------------------- 3.4/3.4 MB 53.9 MB/s eta 0:00:00
Collecting aiohappyeyeballs>=2.5.0 (from aiohttp->dashscope)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Collecting aiosignal>=1.4.0 (from aiohttp->dashscope)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/fb/76/641ae371508676492379f16e2fa48f4e2c11741bd63c48be4b12a6b09cba/aiosignal-1.4.0-py3-none-any.whl (7.5 kB)
Collecting attrs>=17.3.0 (from aiohttp->dashscope)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl (63 kB)
Collecting frozenlist>=1.1.1 (from aiohttp->dashscope)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/0b/15/c026e9a9fc17585a9d461f65d8593d281fedf55fbf7eb53f16c6df2392f9/frozenlist-1.7.0-cp312-cp312-win_amd64.whl (43 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp->dashscope)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/6a/a3/0fbc7afdf7cb1aa12a086b02959307848eb6bcc8f66fcb66c0cb57e2a2c1/multidict-6.6.3-cp312-cp312-win_amd64.whl (45 kB)
Collecting propcache>=0.2.0 (from aiohttp->dashscope)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/19/61/d582be5d226cf79071681d1b46b848d6cb03d7b70af7063e33a2787eaa03/propcache-0.3.2-cp312-cp312-win_amd64.whl (41 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp->dashscope)
  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/eb/83/5d9092950565481b413b31a23e75dd3418ff0a277d6e0abf3729d4d1ce25/yarl-1.20.1-cp312-cp312-win_amd64.whl (86 kB)
Collecting cffi>=1.14 (from cryptography->dashscope)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/50/b9/db34c4755a7bd1cb2d1603ac3863f22bcecbd1ba29e5ee841a4bc510b294/cffi-1.17.1-cp312-cp312-win_amd64.whl (181 kB)
     ------------------------------------- 182.0/182.0 kB 10.7 MB/s eta 0:00:00
Requirement already satisfied: charset_normalizer<4,>=2 in d:\pyenv\pyenv-win\versions\3.12.3\lib\site-packages (from requests->dashscope) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in d:\pyenv\pyenv-win\versions\3.12.3\lib\site-packages (from requests->dashscope) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in d:\pyenv\pyenv-win\versions\3.12.3\lib\site-packages (from requests->dashscope) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in d:\pyenv\pyenv-win\versions\3.12.3\lib\site-packages (from requests->dashscope) (2025.7.14)
Requirement already satisfied: typing-extensions>=4.2 in d:\pyenv\pyenv-win\versions\3.12.3\lib\site-packages (from aiosignal>=1.4.0->aiohttp->dashscope) (4.14.1)
Collecting pycparser (from cffi>=1.14->cryptography->dashscope)
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl (117 kB)
     ---------------------------------------- 117.6/117.6 kB ? eta 0:00:00
Installing collected packages: websocket-client, pycparser, propcache, multidict, frozenlist, attrs, aiohappyeyeballs, yarl, cffi, aiosignal, cryptography, aiohttp, dashscope
Successfully installed aiohappyeyeballs-2.6.1 aiohttp-3.12.15 aiosignal-1.4.0 attrs-25.3.0 cffi-1.17.1 cryptography-45.0.5 dashscope-1.24.1 frozenlist-1.7.0 multidict-6.6.3 propcache-0.3.2 pycparser-2.22 websocket-client-1.8.0 yarl-1.20.1
