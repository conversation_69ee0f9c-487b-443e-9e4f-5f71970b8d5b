#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云Paraformer语音识别环境设置脚本
"""

import os
import sys
import subprocess

def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    
    packages = [
        "dashscope>=1.20.0",
        "requests>=2.25.0"
    ]
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {package} 安装失败: {e}")
            return False
    
    return True

def setup_api_key():
    """设置API Key"""
    print("\n" + "="*50)
    print("API Key 设置")
    print("="*50)
    
    current_key = os.getenv('DASHSCOPE_API_KEY')
    if current_key:
        print(f"当前环境变量中的API Key: {current_key[:10]}...")
        choice = input("是否要更新API Key? (y/n): ").lower()
        if choice != 'y':
            return True
    
    print("\n请选择API Key设置方式:")
    print("1. 设置环境变量（推荐）")
    print("2. 在代码中设置（不推荐用于生产环境）")
    print("3. 跳过设置")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        api_key = input("请输入您的API Key: ").strip()
        if api_key:
            if sys.platform == "win32":
                print(f"\n请在命令行中执行以下命令设置环境变量:")
                print(f"set DASHSCOPE_API_KEY={api_key}")
            else:
                print(f"\n请在终端中执行以下命令设置环境变量:")
                print(f"export DASHSCOPE_API_KEY={api_key}")
            
            print("\n或者将以下内容添加到您的配置文件中:")
            if sys.platform == "win32":
                print("Windows: 系统属性 -> 高级 -> 环境变量")
            else:
                print("Linux/Mac: ~/.bashrc 或 ~/.zshrc")
                print(f"export DASHSCOPE_API_KEY={api_key}")
        else:
            print("未输入API Key")
            
    elif choice == "2":
        api_key = input("请输入您的API Key: ").strip()
        if api_key:
            print(f"\n请在代码中添加以下内容:")
            print(f"import dashscope")
            print(f"dashscope.api_key = '{api_key}'")
        else:
            print("未输入API Key")
            
    elif choice == "3":
        print("跳过API Key设置")
    else:
        print("无效选择")
    
    return True

def test_installation():
    """测试安装"""
    print("\n" + "="*50)
    print("测试安装")
    print("="*50)
    
    try:
        import dashscope
        print("✓ dashscope 导入成功")
        
        import requests
        print("✓ requests 导入成功")
        
        # 检查API Key
        api_key = os.getenv('DASHSCOPE_API_KEY')
        if api_key:
            print(f"✓ 检测到环境变量 DASHSCOPE_API_KEY: {api_key[:10]}...")
        else:
            print("⚠ 未检测到环境变量 DASHSCOPE_API_KEY")
            print("  请确保已设置API Key")
        
        print("\n安装测试完成!")
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def show_usage():
    """显示使用说明"""
    print("\n" + "="*50)
    print("使用说明")
    print("="*50)
    
    print("\n1. 快速开始:")
    print("   python simple_audio_recognition.py")
    
    print("\n2. 完整功能:")
    print("   python paraformer_speech_recognition.py")
    
    print("\n3. 查看文档:")
    print("   查看 PARAFORMER_README.md 文件")
    
    print("\n4. 获取API Key:")
    print("   访问 https://bailian.console.aliyun.com/")
    print("   开通服务并获取API Key")

def main():
    """主函数"""
    print("阿里云Paraformer语音识别环境设置")
    print("="*50)
    
    # 安装依赖
    if not install_dependencies():
        print("依赖安装失败，请检查网络连接或手动安装")
        return
    
    # 设置API Key
    setup_api_key()
    
    # 测试安装
    if not test_installation():
        print("安装测试失败，请检查依赖是否正确安装")
        return
    
    # 显示使用说明
    show_usage()
    
    print("\n" + "="*50)
    print("设置完成! 现在可以开始使用语音识别功能了。")
    print("="*50)

if __name__ == "__main__":
    main()
