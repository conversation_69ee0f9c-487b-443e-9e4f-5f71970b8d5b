#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云Paraformer说话人分离功能演示
展示时间戳校准和说话人识别功能
"""

import os
import json
from http import HTTPStatus
from dashscope.audio.asr import Transcription
import dashscope

def setup_api_key():
    """设置API Key"""
    # 使用提供的API Key
    api_key = "sk-23c27f057aad4c52a7a778119edfb87e"
    
    if api_key:
        dashscope.api_key = api_key
        print(f"✓ API Key已设置: {api_key[:10]}...")
        return True
    return False

def format_time(milliseconds):
    """将毫秒转换为可读的时间格式"""
    seconds = milliseconds / 1000
    minutes = int(seconds // 60)
    seconds = seconds % 60
    return f"{minutes:02d}:{seconds:05.2f}"

def recognize_with_speaker_diarization():
    """使用说话人分离功能进行语音识别"""
    
    # 您提供的音频文件URL
    audio_url = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3"
    
    try:
        print("开始语音识别（启用说话人分离）...")
        print(f"音频文件: {audio_url}")
        print("功能配置:")
        print("  ✓ 时间戳校准 (timestamp_alignment_enabled)")
        print("  ✓ 说话人分离 (diarization_enabled)")
        print("  ✓ 过滤语气词 (disfluency_removal_enabled)")
        
        # 提交识别任务
        task_response = Transcription.async_call(
            model='paraformer-v2',  # 使用最新的v2模型
            file_urls=[audio_url],
            language_hints=['zh', 'en'],  # 支持中英文
            disfluency_removal_enabled=True,  # 过滤语气词
            timestamp_alignment_enabled=True,  # 启用时间戳校准
            diarization_enabled=True,  # 启用说话人分离
            # speaker_count=2,  # 可选：指定说话人数量
        )
        
        if task_response.status_code != HTTPStatus.OK:
            print(f"任务提交失败: {task_response.message}")
            return
        
        print(f"\n任务ID: {task_response.output.task_id}")
        print("等待识别完成...")
        
        # 等待任务完成
        result = Transcription.wait(task=task_response.output.task_id)
        
        if result.status_code == HTTPStatus.OK:
            print("识别完成!")
            
            # 处理结果
            print(f"任务状态: {result.output.task_status}")
            
            if result.output.results:
                for i, item in enumerate(result.output.results):
                    print(f"\n{'='*60}")
                    print(f"处理结果 {i+1}")
                    print(f"{'='*60}")
                    
                    # 获取结果信息
                    if isinstance(item, dict):
                        file_url = item.get('file_url', '未知')
                        subtask_status = item.get('subtask_status', '未知')
                        transcription_url = item.get('transcription_url', '')
                    else:
                        file_url = getattr(item, 'file_url', '未知')
                        subtask_status = getattr(item, 'subtask_status', '未知')
                        transcription_url = getattr(item, 'transcription_url', '')
                    
                    print(f"音频文件: {file_url}")
                    print(f"子任务状态: {subtask_status}")
                    
                    if subtask_status == 'SUCCEEDED' and transcription_url:
                        # 下载识别结果
                        try:
                            import requests
                            response = requests.get(transcription_url)
                            if response.status_code == 200:
                                transcription_data = response.json()
                                
                                # 显示音频基本信息
                                properties = transcription_data.get('properties', {})
                                print(f"\n音频信息:")
                                print(f"  格式: {properties.get('audio_format', '未知')}")
                                print(f"  采样率: {properties.get('original_sampling_rate', '未知')} Hz")
                                print(f"  原始时长: {format_time(properties.get('original_duration_in_milliseconds', 0))}")
                                
                                # 处理识别结果
                                for transcript in transcription_data['transcripts']:
                                    channel_id = transcript['channel_id']
                                    content_duration = transcript['content_duration_in_milliseconds']
                                    full_text = transcript['text']
                                    
                                    print(f"\n音轨 {channel_id} 识别结果:")
                                    print(f"  语音内容时长: {format_time(content_duration)}")
                                    print(f"  完整文本: {full_text}")
                                    
                                    # 按说话人分组显示
                                    speakers = {}
                                    for sentence in transcript['sentences']:
                                        speaker_id = sentence.get('speaker_id', 0)
                                        if speaker_id not in speakers:
                                            speakers[speaker_id] = []
                                        speakers[speaker_id].append(sentence)
                                    
                                    print(f"\n检测到 {len(speakers)} 个说话人:")
                                    
                                    # 显示每个说话人的内容
                                    for speaker_id, sentences in speakers.items():
                                        print(f"\n说话人 {speaker_id}:")
                                        print("-" * 40)
                                        
                                        for sentence in sentences:
                                            start_time = format_time(sentence['begin_time'])
                                            end_time = format_time(sentence['end_time'])
                                            text = sentence['text']
                                            
                                            print(f"  [{start_time} - {end_time}]: {text}")
                                    
                                    # 显示时间线视图
                                    print(f"\n时间线视图:")
                                    print("-" * 60)
                                    
                                    # 按时间顺序排序所有句子
                                    all_sentences = sorted(transcript['sentences'], key=lambda x: x['begin_time'])
                                    
                                    for sentence in all_sentences:
                                        start_time = format_time(sentence['begin_time'])
                                        end_time = format_time(sentence['end_time'])
                                        speaker_id = sentence.get('speaker_id', 0)
                                        text = sentence['text']
                                        
                                        print(f"  [{start_time} - {end_time}] 说话人{speaker_id}: {text}")
                                    
                                    # 如果有词级别的时间戳，显示部分示例
                                    if 'sentences' in transcript and transcript['sentences']:
                                        first_sentence = transcript['sentences'][0]
                                        if 'words' in first_sentence and first_sentence['words']:
                                            print(f"\n词级别时间戳示例（前10个词）:")
                                            print("-" * 40)
                                            
                                            for i, word in enumerate(first_sentence['words'][:10]):
                                                start_time = format_time(word['begin_time'])
                                                end_time = format_time(word['end_time'])
                                                text = word['text']
                                                punctuation = word.get('punctuation', '')
                                                
                                                print(f"  [{start_time} - {end_time}]: '{text}'{punctuation}")
                            else:
                                print(f"下载识别结果失败: HTTP {response.status_code}")
                        except Exception as e:
                            print(f"处理识别结果时出错: {str(e)}")
                    else:
                        error_msg = item.get('message', '未知错误') if isinstance(item, dict) else getattr(item, 'message', '未知错误')
                        print(f"识别失败: {error_msg}")
            else:
                print("没有识别结果")
        else:
            print(f"识别失败: {result.message}")
            
    except Exception as e:
        print(f"语音识别过程中出错: {str(e)}")

def main():
    """主函数"""
    print("阿里云Paraformer说话人分离功能演示")
    print("="*60)
    
    # 设置API Key
    if not setup_api_key():
        return
    
    # 执行语音识别
    recognize_with_speaker_diarization()
    
    print(f"\n{'='*60}")
    print("说话人分离功能说明:")
    print("- timestamp_alignment_enabled: 提供精确的词级别时间戳")
    print("- diarization_enabled: 自动识别不同说话人")
    print("- speaker_id: 用数字标识不同的说话人（从0开始）")
    print("- 仅支持单声道音频的说话人分离")
    print("="*60)

if __name__ == "__main__":
    main()
