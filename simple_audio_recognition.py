#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的语音识别示例
针对指定音频文件进行识别
"""

import os
import json
from http import HTTPStatus
from dashscope.audio.asr import Transcription
import dashscope

def setup_api_key():
    """设置API Key"""
    # 使用提供的API Key
    api_key = "sk-23c27f057aad4c52a7a778119edfb87e"

    if api_key:
        dashscope.api_key = api_key
        print(f"✓ API Key已设置: {api_key[:10]}...")
        return True
    elif not os.getenv('DASHSCOPE_API_KEY'):
        print("请设置API Key!")
        print("方式1: 修改代码中的api_key变量")
        print("方式2: 设置环境变量 DASHSCOPE_API_KEY")
        return False
    return True

def recognize_audio():
    """识别指定的音频文件"""
    
    # 您提供的音频文件URL
    audio_url = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3"
    
    try:
        print("开始语音识别...")
        print(f"音频文件: {audio_url}")
        
        # 提交识别任务
        task_response = Transcription.async_call(
            model='paraformer-v2',  # 使用最新的v2模型
            file_urls=[audio_url],
            language_hints=['zh', 'en'],  # 支持中英文
            disfluency_removal_enabled=True,  # 过滤语气词
            timestamp_alignment_enabled=True,  # 启用时间戳
        )
        
        if task_response.status_code != HTTPStatus.OK:
            print(f"任务提交失败: {task_response.message}")
            return
        
        print(f"任务ID: {task_response.output.task_id}")
        print("等待识别完成...")
        
        # 等待任务完成
        result = Transcription.wait(task=task_response.output.task_id)
        
        if result.status_code == HTTPStatus.OK:
            print("识别完成!")
            
            # 处理结果
            print(f"任务状态: {result.output.task_status}")
            print(f"结果数量: {len(result.output.results) if result.output.results else 0}")

            if result.output.results:
                for i, item in enumerate(result.output.results):
                    print(f"\n处理结果 {i+1}:")

                    # 检查item的类型和属性
                    if isinstance(item, dict):
                        file_url = item.get('file_url', '未知')
                        subtask_status = item.get('subtask_status', '未知')
                        transcription_url = item.get('transcription_url', '')
                    else:
                        file_url = getattr(item, 'file_url', '未知')
                        subtask_status = getattr(item, 'subtask_status', '未知')
                        transcription_url = getattr(item, 'transcription_url', '')

                    print(f"音频文件: {file_url}")
                    print(f"子任务状态: {subtask_status}")

                    if subtask_status == 'SUCCEEDED' and transcription_url:
                        print(f"识别结果URL: {transcription_url}")

                        # 下载识别结果
                        try:
                            import requests
                            response = requests.get(transcription_url)
                            if response.status_code == 200:
                                transcription_data = response.json()

                                # 显示识别文本
                                for transcript in transcription_data['transcripts']:
                                    print(f"\n完整识别文本: {transcript['text']}")
                                    print(f"音频时长: {transcript['content_duration_in_milliseconds']}ms")

                                    # 显示句子级别的结果
                                    print("\n详细结果:")
                                    for sentence in transcript['sentences']:
                                        start_time = sentence['begin_time'] / 1000  # 转换为秒
                                        end_time = sentence['end_time'] / 1000
                                        print(f"  [{start_time:.2f}s - {end_time:.2f}s]: {sentence['text']}")

                                        # 如果有说话人信息
                                        if 'speaker_id' in sentence:
                                            print(f"    说话人: {sentence['speaker_id']}")
                            else:
                                print(f"下载识别结果失败: HTTP {response.status_code}")
                        except Exception as e:
                            print(f"处理识别结果时出错: {str(e)}")
                    else:
                        error_msg = item.get('message', '未知错误') if isinstance(item, dict) else getattr(item, 'message', '未知错误')
                        print(f"识别失败: {error_msg}")
            else:
                print("没有识别结果")
        else:
            print(f"识别失败: {result.message}")
            
    except Exception as e:
        print(f"语音识别过程中出错: {str(e)}")

def main():
    """主函数"""
    print("阿里云Paraformer语音识别示例")
    print("="*50)
    
    # 设置API Key
    if not setup_api_key():
        return
    
    # 执行语音识别
    recognize_audio()

if __name__ == "__main__":
    main()
