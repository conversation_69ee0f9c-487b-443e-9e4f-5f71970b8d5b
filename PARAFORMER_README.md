# 阿里云Paraformer语音识别示例

本项目演示如何使用阿里云DashScope SDK进行语音识别，基于Paraformer模型。

## 功能特性

- 支持多种音频格式：aac、amr、avi、flac、flv、m4a、mkv、mov、mp3、mp4、mpeg、ogg、opus、wav、webm、wma、wmv
- 支持多语言识别：中文、英文、日语、韩语、德语、法语、俄语等
- 支持标点符号预测和逆文本正则化（ITN）
- 支持时间戳校准和说话人分离
- 支持热词定制

## 安装依赖

```bash
pip install dashscope>=1.20.0 requests>=2.25.0
```

## 配置API Key

### 方式1：环境变量（推荐）

```bash
# Windows
set DASHSCOPE_API_KEY=your_api_key_here

# Linux/Mac
export DASHSCOPE_API_KEY=your_api_key_here
```

### 方式2：修改代码

在代码中直接设置API Key（不推荐用于生产环境）：

```python
import dashscope
dashscope.api_key = "your_api_key_here"
```

## 使用示例

### 快速开始

运行简单示例：

```bash
python simple_audio_recognition.py
```

这个示例会识别指定的音频文件：
`https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3`

### 完整功能示例

运行完整示例：

```bash
python paraformer_speech_recognition.py
```

## 代码说明

### simple_audio_recognition.py

简化版本，专门用于识别单个音频文件，包含：
- API Key设置
- 音频识别
- 结果解析和显示

### paraformer_speech_recognition.py

完整版本，包含：
- ParaformerASR类封装
- 同步和异步识别方法
- 完整的参数配置
- 错误处理和结果处理

## 主要参数说明

### 模型选择

- `paraformer-v2`：推荐使用，支持多语种
- `paraformer-8k-v2`：适用于8kHz采样率的中文音频
- `paraformer-v1`：中英文混合场景
- `paraformer-8k-v1`：8kHz中文音频
- `paraformer-mtl-v1`：多语言模型

### 常用参数

```python
Transcription.async_call(
    model='paraformer-v2',                    # 模型名称
    file_urls=['audio_url'],                  # 音频文件URL列表
    language_hints=['zh', 'en'],              # 语言提示
    disfluency_removal_enabled=True,          # 过滤语气词
    timestamp_alignment_enabled=True,         # 时间戳校准
    diarization_enabled=False,                # 说话人分离
    speaker_count=2,                          # 说话人数量
    vocabulary_id='your_vocabulary_id'        # 热词ID
)
```

## 音频要求

- **格式**：支持常见音视频格式
- **大小**：不超过2GB
- **时长**：12小时以内
- **访问**：必须是公网可访问的URL
- **采样率**：根据模型而定

## 识别结果格式

识别结果包含以下信息：

```json
{
    "file_url": "音频文件URL",
    "transcripts": [
        {
            "channel_id": 0,
            "text": "完整识别文本",
            "content_duration_in_milliseconds": 3720,
            "sentences": [
                {
                    "begin_time": 100,
                    "end_time": 3820,
                    "text": "句子文本",
                    "sentence_id": 1,
                    "speaker_id": 0,
                    "words": [
                        {
                            "begin_time": 100,
                            "end_time": 596,
                            "text": "单词",
                            "punctuation": ""
                        }
                    ]
                }
            ]
        }
    ]
}
```

## 错误处理

常见错误及解决方案：

1. **FILE_DOWNLOAD_FAILED**：检查音频URL是否可访问
2. **FILE_TOO_LARGE**：音频文件超过2GB，需要分段
3. **AUDIO_DURATION_TOO_LONG**：音频超过12小时，需要切分
4. **FILE_CHECK_FAILED**：音频格式不支持

## 注意事项

1. 音频文件必须通过公网URL访问，不支持本地文件
2. 识别结果有效期为24小时
3. 建议使用最新的paraformer-v2模型
4. API Key请妥善保管，不要硬编码在代码中

## 获取API Key

1. 访问[阿里云百炼平台](https://bailian.console.aliyun.com/)
2. 开通服务并获取API Key
3. 配置到环境变量或代码中

## 更多信息

- [官方文档](https://help.aliyun.com/zh/model-studio/paraformer-recorded-speech-recognition-python-sdk)
- [GitHub示例](https://github.com/aliyun/alibabacloud-bailian-speech-demo)
