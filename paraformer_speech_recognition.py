#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云Paraformer语音识别示例
使用DashScope SDK进行录音文件识别
"""

import os
import json
import time
from http import HTTPStatus
from dashscope.audio.asr import Transcription
import dashscope

class ParaformerASR:
    """Paraformer语音识别类"""
    
    def __init__(self, api_key=None):
        """
        初始化语音识别客户端
        
        Args:
            api_key (str, optional): API密钥，如果不提供则从环境变量DASHSCOPE_API_KEY获取
        """
        if api_key:
            dashscope.api_key = api_key
        elif not os.getenv('DASHSCOPE_API_KEY'):
            raise ValueError("请设置DASHSCOPE_API_KEY环境变量或提供api_key参数")
    
    def recognize_sync(self, file_urls, model='paraformer-v2', **kwargs):
        """
        同步语音识别（提交任务后等待完成）
        
        Args:
            file_urls (list): 音频文件URL列表
            model (str): 使用的模型名称，默认paraformer-v2
            **kwargs: 其他参数
                - language_hints: 语言提示，如['zh', 'en']
                - disfluency_removal_enabled: 是否过滤语气词
                - timestamp_alignment_enabled: 是否启用时间戳校准
                - diarization_enabled: 是否启用说话人分离
                - speaker_count: 说话人数量
                - vocabulary_id: 热词ID
        
        Returns:
            dict: 识别结果
        """
        try:
            print(f"开始提交语音识别任务...")
            print(f"音频文件: {file_urls}")
            print(f"使用模型: {model}")
            
            # 提交异步任务
            task_response = Transcription.async_call(
                model=model,
                file_urls=file_urls,
                **kwargs
            )
            
            if task_response.status_code != HTTPStatus.OK:
                raise Exception(f"任务提交失败: {task_response.message}")
            
            task_id = task_response.output.task_id
            print(f"任务ID: {task_id}")
            print("任务已提交，等待处理完成...")
            
            # 等待任务完成
            transcribe_response = Transcription.wait(task=task_id)
            
            if transcribe_response.status_code == HTTPStatus.OK:
                print("语音识别完成!")
                return self._process_results(transcribe_response.output)
            else:
                raise Exception(f"识别失败: {transcribe_response.message}")
                
        except Exception as e:
            print(f"语音识别出错: {str(e)}")
            return None
    
    def recognize_async(self, file_urls, model='paraformer-v2', **kwargs):
        """
        异步语音识别（提交任务后轮询结果）
        
        Args:
            file_urls (list): 音频文件URL列表
            model (str): 使用的模型名称
            **kwargs: 其他参数
        
        Returns:
            dict: 识别结果
        """
        try:
            print(f"开始提交语音识别任务...")
            print(f"音频文件: {file_urls}")
            print(f"使用模型: {model}")
            
            # 提交异步任务
            transcribe_response = Transcription.async_call(
                model=model,
                file_urls=file_urls,
                **kwargs
            )
            
            if transcribe_response.status_code != HTTPStatus.OK:
                raise Exception(f"任务提交失败: {transcribe_response.message}")
            
            task_id = transcribe_response.output.task_id
            print(f"任务ID: {task_id}")
            print("开始轮询任务状态...")
            
            # 轮询任务状态
            while True:
                status = transcribe_response.output.task_status
                print(f"当前状态: {status}")
                
                if status == 'SUCCEEDED':
                    print("语音识别完成!")
                    return self._process_results(transcribe_response.output)
                elif status == 'FAILED':
                    raise Exception("任务执行失败")
                elif status in ['PENDING', 'RUNNING']:
                    print("任务处理中，等待5秒后重新查询...")
                    time.sleep(5)
                    transcribe_response = Transcription.fetch(task=task_id)
                else:
                    raise Exception(f"未知任务状态: {status}")
                    
        except Exception as e:
            print(f"语音识别出错: {str(e)}")
            return None
    
    def _process_results(self, output):
        """
        处理识别结果
        
        Args:
            output: 识别输出结果
            
        Returns:
            dict: 处理后的结果
        """
        results = []
        
        if hasattr(output, 'results') and output.results:
            for result in output.results:
                if result.subtask_status == 'SUCCEEDED':
                    # 下载并解析识别结果
                    transcription_data = self._download_transcription(result.transcription_url)
                    if transcription_data:
                        results.append({
                            'file_url': result.file_url,
                            'transcription': transcription_data,
                            'status': 'success'
                        })
                else:
                    results.append({
                        'file_url': result.file_url,
                        'error': getattr(result, 'message', '识别失败'),
                        'status': 'failed'
                    })
        
        return {
            'task_id': output.task_id,
            'task_status': output.task_status,
            'results': results,
            'task_metrics': output.task_metrics if hasattr(output, 'task_metrics') else None
        }
    
    def _download_transcription(self, transcription_url):
        """
        下载识别结果文件
        
        Args:
            transcription_url (str): 结果文件URL
            
        Returns:
            dict: 识别结果数据
        """
        try:
            import requests
            response = requests.get(transcription_url)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"下载识别结果失败: HTTP {response.status_code}")
                return None
        except Exception as e:
            print(f"下载识别结果出错: {str(e)}")
            return None


def main():
    """主函数示例"""

    # 初始化语音识别客户端
    # 使用提供的API Key
    asr = ParaformerASR(api_key="sk-23c27f057aad4c52a7a778119edfb87e")

    # 方式2: 从环境变量获取API Key
    # asr = ParaformerASR()
    
    # 要识别的音频文件URL
    audio_urls = [
        "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3"
    ]
    
    # 示例1: 使用同步方式识别（推荐）
    print("=== 同步识别示例 ===")
    result = asr.recognize_sync(
        file_urls=audio_urls,
        model='paraformer-v2',  # 推荐使用最新的v2模型
        language_hints=['zh', 'en'],  # 指定语言提示
        disfluency_removal_enabled=True,  # 过滤语气词
        timestamp_alignment_enabled=True,  # 启用时间戳校准
        diarization_enabled=True,  # 启用说话人分离
    )
    
    if result:
        print("\n识别结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 提取文本内容
        for item in result['results']:
            if item['status'] == 'success':
                transcription = item['transcription']
                print(f"\n音频文件: {item['file_url']}")
                print(f"识别文本: {transcription['transcripts'][0]['text']}")
                
                # 显示详细的句子级别结果
                for transcript in transcription['transcripts']:
                    print(f"\n音轨 {transcript['channel_id']} 识别结果:")
                    for sentence in transcript['sentences']:
                        print(f"  时间: {sentence['begin_time']}ms - {sentence['end_time']}ms")
                        print(f"  文本: {sentence['text']}")
    
    print("\n" + "="*50)
    
    # 示例2: 使用异步方式识别
    print("=== 异步识别示例 ===")
    result2 = asr.recognize_async(
        file_urls=audio_urls,
        model='paraformer-v2',
        language_hints=['zh', 'en'],
        timestamp_alignment_enabled=True,  # 启用时间戳校准
        diarization_enabled=True,  # 启用说话人分离
    )
    
    if result2:
        print("\n异步识别结果:")
        print(json.dumps(result2, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    # API Key已在代码中设置
    # 如果需要使用环境变量，请取消下面的注释并设置您的API Key
    # os.environ['DASHSCOPE_API_KEY'] = 'sk-23c27f057aad4c52a7a778119edfb87e'

    main()
