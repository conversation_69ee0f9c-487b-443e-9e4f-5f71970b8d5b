#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云Paraformer语音识别测试脚本
"""

import os
import sys
import json
from http import HTTPStatus

def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    
    try:
        import dashscope
        print("✓ dashscope 已安装")
    except ImportError:
        print("✗ dashscope 未安装，请运行: pip install dashscope>=1.20.0")
        return False
    
    try:
        import requests
        print("✓ requests 已安装")
    except ImportError:
        print("✗ requests 未安装，请运行: pip install requests>=2.25.0")
        return False
    
    return True

def check_api_key():
    """检查API Key"""
    print("\n检查API Key...")
    
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if api_key:
        print(f"✓ 环境变量 DASHSCOPE_API_KEY 已设置: {api_key[:10]}...")
        return api_key
    else:
        print("✗ 环境变量 DASHSCOPE_API_KEY 未设置")
        
        # 尝试从用户输入获取
        api_key = input("请输入您的API Key (或按回车跳过): ").strip()
        if api_key:
            import dashscope
            dashscope.api_key = api_key
            print("✓ API Key 已临时设置")
            return api_key
        else:
            print("⚠ 跳过API Key设置，将使用模拟测试")
            return None

def test_audio_url():
    """测试音频URL可访问性"""
    print("\n测试音频URL...")
    
    audio_url = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3"
    
    try:
        import requests
        response = requests.head(audio_url, timeout=10)
        if response.status_code == 200:
            print(f"✓ 音频URL可访问: {audio_url}")
            content_length = response.headers.get('content-length')
            if content_length:
                size_mb = int(content_length) / (1024 * 1024)
                print(f"  文件大小: {size_mb:.2f} MB")
            return True
        else:
            print(f"✗ 音频URL不可访问: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 测试音频URL时出错: {str(e)}")
        return False

def test_api_connection(api_key):
    """测试API连接"""
    print("\n测试API连接...")
    
    if not api_key:
        print("⚠ 跳过API连接测试（无API Key）")
        return False
    
    try:
        from dashscope.audio.asr import Transcription
        
        # 使用一个简单的测试音频
        test_url = "https://dashscope.oss-cn-beijing.aliyuncs.com/samples/audio/paraformer/hello_world_female2.wav"
        
        print("提交测试任务...")
        task_response = Transcription.async_call(
            model='paraformer-v2',
            file_urls=[test_url],
            language_hints=['zh', 'en']
        )
        
        if task_response.status_code == HTTPStatus.OK:
            print("✓ API连接成功")
            print(f"  任务ID: {task_response.output.task_id}")
            print(f"  任务状态: {task_response.output.task_status}")
            return True
        else:
            print(f"✗ API连接失败: {task_response.message}")
            return False
            
    except Exception as e:
        print(f"✗ API连接测试出错: {str(e)}")
        return False

def run_mock_test():
    """运行模拟测试"""
    print("\n运行模拟测试...")
    
    # 模拟识别结果
    mock_result = {
        "task_id": "mock-task-id",
        "task_status": "SUCCEEDED",
        "results": [
            {
                "file_url": "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3",
                "status": "success",
                "transcription": {
                    "transcripts": [
                        {
                            "channel_id": 0,
                            "text": "这是一个模拟的语音识别结果。",
                            "content_duration_in_milliseconds": 5000,
                            "sentences": [
                                {
                                    "begin_time": 100,
                                    "end_time": 5000,
                                    "text": "这是一个模拟的语音识别结果。",
                                    "sentence_id": 1
                                }
                            ]
                        }
                    ]
                }
            }
        ]
    }
    
    print("✓ 模拟测试完成")
    print("模拟识别结果:")
    print(json.dumps(mock_result, indent=2, ensure_ascii=False))
    return True

def main():
    """主函数"""
    print("阿里云Paraformer语音识别测试")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装依赖包:")
        print("pip install dashscope>=1.20.0 requests>=2.25.0")
        return
    
    # 检查API Key
    api_key = check_api_key()
    
    # 测试音频URL
    audio_accessible = test_audio_url()
    
    # 测试API连接
    api_working = test_api_connection(api_key)
    
    # 运行模拟测试
    mock_test = run_mock_test()
    
    # 总结
    print("\n" + "="*50)
    print("测试总结")
    print("="*50)
    
    print(f"依赖包: {'✓' if check_dependencies() else '✗'}")
    print(f"API Key: {'✓' if api_key else '✗'}")
    print(f"音频URL: {'✓' if audio_accessible else '✗'}")
    print(f"API连接: {'✓' if api_working else '✗'}")
    print(f"模拟测试: {'✓' if mock_test else '✗'}")
    
    if api_key and audio_accessible and api_working:
        print("\n🎉 所有测试通过! 可以开始使用语音识别功能。")
        print("运行命令: python simple_audio_recognition.py")
    elif api_key and audio_accessible:
        print("\n⚠ API连接测试失败，但基础环境正常。")
        print("请检查API Key是否有效或网络连接。")
    else:
        print("\n❌ 部分测试失败，请检查配置。")
        print("参考文档: PARAFORMER_README.md")
    
    print("\n获取API Key: https://bailian.console.aliyun.com/")

if __name__ == "__main__":
    main()
